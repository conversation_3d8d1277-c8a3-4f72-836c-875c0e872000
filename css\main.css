:root {
        --color-primary: #1e40af;
        --color-secondary: #f59e42;
        --color-background: #f8fafc;
        --color-surface: #ffffff;
        --color-text: #1e293b;
        --color-text-muted: #64748b;
        --color-success: #22c55e;
        --color-error: #ef4444;
        --color-border: #e2e8f0;
        --color-hover: #f1f5f9;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
html, body {
    overflow-x: hidden;
    max-width: 100vw;
}

      body {
        font-family: 'Cairo', 'Segoe UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
        background: #f8fafc;
        color: var(--color-text);
        line-height: 1.6;
        font-size: 16px;
      }

      /* Header Styles */
      header {
        background: var(--color-surface);
        box-shadow: var(--shadow-md);
        position: sticky;
        top: 0;
        z-index: 1000;
        border-bottom: 1px solid var(--color-border);
      }
      button {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
        height: 40px  ;
        width: 100px;
        background: #011440;
        color: white;
        text-decoration: none;
        font-size: 14pt;
        font-weight: 500;
        transition: all 0.3s ease;
        animation: fadeInUp 1s ease-out 0.6s both;
}
      header:hover button {
      background: var(--color-secondary);        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }
      .header-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 2rem;
        height: 80px;
      }

      .logo-area {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .logo-area img {
        height: 85px;
        width: 85px;
        object-fit: contain;
      }

      .logo-text {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--color-primary);
        text-decoration: none;
      }

      /* Navigation Styles */
      nav {
        display: flex;
        align-items: center;
      }

      .nav-list {
        display: flex;
        list-style: none;
        gap: 0.5rem;
      }

      .nav-item {
        position: relative;
      }

      .nav-link {
        display: block;
        padding: 0.75rem 1rem;
        color: var(--color-text);
        text-decoration: none;
        font-weight: 500;
        border-radius: none;
        transition: border-color 0.3s, color 0.3s; /* Smooth transition */
        cursor: pointer;
        white-space: nowrap;
        border-bottom: 2px solid transparent;
      }

      .nav-link:hover {
    
        color:var(--color-primary);
        border-bottom: 2px solid var(--color-primary);
      }

      /* Dropdown Styles */
      .dropdown {
        position: absolute;
        top: calc(100% + 8px); /* 8px gap below the trigger */
        left: 50%;
        background: var(--color-surface);
        min-width: 280px;
        box-shadow: var(--shadow-lg);
        border-radius: 0.75rem;
        border: 1px solid var(--color-border);
        opacity: 0;
        visibility: hidden;
        transform: translateX(-50%) translateY(-10px);
        transition: all 0.3s ease;
        z-index: 1000;
      }

      .nav-item:hover .dropdown {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
      }

      .dropdown-item {
        list-style: none;
      }

      .dropdown-link {
        display: block;
        padding: 0.75rem 1.25rem;
        color: var(--color-text);
        text-decoration: none;
        border-bottom: 1px solid var(--color-border);
        transition: all 0.3s ease;
      }

      .dropdown-link:hover {
        background: var(--color-hover);
        color: var(--color-primary);
        padding-right: 1.5rem;
      }

      .dropdown-item:last-child .dropdown-link {
        border-bottom: none;
        border-radius: 0 0 0.75rem 0.75rem;
      }

      .dropdown-item:first-child .dropdown-link {
        border-radius: 0.75rem 0.75rem 0 0;
      }

      /* Multi-column dropdown */
      .multi-column-dropdown {
        min-width: 600px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0;
      }

      .dropdown-column {
        border-left: 1px solid var(--color-border);
      }

      .dropdown-column:first-child {
        border-left: none;
      }

      .dropdown-column .dropdown-link {
       border-bottom: 1px solid var(--color-border);
        border-left: none;
      }

      /* Mobile Menu Button */
      .mobile-menu-btn {
        display: none;
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--color-text);
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.5rem;
        transition: background 0.3s ease;
      }

      .mobile-menu-btn:hover {
        background: var(--color-hover);
      }

      /* Hero Section */
      .hero {
        background: #011440;
        color: white;
        padding: 6rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .hero-content {
        position: relative;
        z-index: 1;
        max-width: 800px;
        margin: 0 auto;
      }

      .hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        animation: fadeInUp 1s ease-out;
      }

      .hero p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.3s both;
      }

      .cta-button {
        display: inline-block;
        background: var(--color-secondary);
        color: white;
        padding: 1rem 2rem;
        text-decoration: none;
        border-radius: 0.75rem;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-md);
        animation: fadeInUp 1s ease-out 0.6s both;
      }

      .cta-button:hover {
        background: #e8710c;
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Content Section */
      .content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 4rem 2rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 3rem;
      }

      article {
        background: var(--color-surface);
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--color-border);
        transition: all 0.3s ease;
      }

      article:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-4px);
      }

      article h2 {
        color: var(--color-primary);
        font-size: 1.75rem;
        font-weight: 600;
        margin-bottom: 1rem;
        border-bottom: 3px solid var(--color-secondary);
        padding-bottom: 0.5rem;
      }

      article p {
        line-height: 1.8;
        color: var(--color-text-muted);
      }

      /* Features Section */
      .features {
        background: var(--color-surface);
        padding: 4rem 2rem;
        margin: 2rem 0;
      }

      .features-container {
        max-width: 1200px;
        margin: 0 auto;
        text-align: center;
      }

      .features h2 {
        font-size: 2.5rem;
        color: var(--color-primary);
        margin-bottom: 3rem;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
      }

      .feature-card {
        padding: 2rem;
        border-radius: 1rem;
        background: var(--color-background);
        border: 1px solid var(--color-border);
        transition: all 0.3s ease;
      }

      .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
      }

      .feature-icon {
        font-size: 3rem;
        color: var(--color-secondary);
        margin-bottom: 1rem;
      }

      .feature-card h3 {
        color: var(--color-primary);
        font-size: 1.5rem;
        margin-bottom: 1rem;
      }

      /* Footer */
   footer {
  background-color: #011440;
  height: 500px;
  color: white;
  text-align: center;
  direction: rtl;

  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}

.footer-divider {
  width: 80%;
  margin: 10px auto;
  border: none;
  border-top: 1px solid #555;
}

.footer-bottom {
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap; /* in case it wraps on small screens */
  padding: 10px 20px;
}

.links {
  display: flex;
  gap: 10px;
  align-items: center;
}

.links a {
  color: white;
  text-decoration: none;
}
.links a:hover {
  text-decoration: underline;
} 

.divider {
  color: #aaa;
}

      /* Responsive Design */
      @media (max-width: 768px) {
        .header-container {
          padding: 0 1rem;
          height: 65px;
        }
        
        .mobile-menu-btn {
          display: block;
        }

        .nav-list {
          display: none;
          position: fixed;
          top: 65px;
          right: 0;
          left: 0;
          background: var(--color-surface);
          flex-direction: column;
          padding: 1rem;
          box-shadow: var(--shadow-lg);
          border-top: 1px solid var(--color-border);
          max-height: calc(100vh - 65px);
          overflow-y: auto;
        }

        .nav-list.active {
          display: flex;
        }

        .nav-item {
          width: 100%;
        }

        .nav-link {
          padding: 1rem;
          border-bottom: 1px solid var(--color-border);
        }

        .dropdown {
          position: static;
          opacity: 1;
          visibility: visible;
          transform: none;
          box-shadow: none;
          border: none;
          border-radius: 0;
          background: var(--color-hover);
          margin-top: 0.5rem;
          display: none;
        }

        .dropdown.active {
          display: block;
        }

        .multi-column-dropdown {
          grid-template-columns: 1fr;
          min-width: auto;
        }

        .dropdown-column {
          border-left: none;
          border-top: 1px solid var(--color-border);
        }

        .hero {
          padding: 3rem 1rem;
          min-height: 60vh;
        }

        .hero h1 {
          font-size: 2.5rem;
        }

        .content {
          grid-template-columns: 1fr;
          padding: 2rem 1rem;
        }

        .features {
          padding: 3rem 1rem;
        }

        .features h2 {
          font-size: 2rem;
        }
      }

      .footer-divider {
        border: none;
        position: center;
        height: .0625rem;
      
        background-color: #33506c;  width: 80%;
        margin: 0 auto 1rem auto; /* center it */
        }


      .footer-links {
        display: inline-flex;
        color: white;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 2rem;
      }

      @media (max-width: 480px) {
        .hero h1 {
          font-size: 2rem;
        }
        
        .hero p {
          font-size: 1rem;
        }
        
          .cta-button {
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
          }
        }

        /* Custom Scrollbar for Webkit browsers */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-thumb {
  background: #011440; /* Blue color */
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: white;
}

/* For Firefox */
html {
  scrollbar-color: #011440;
}
