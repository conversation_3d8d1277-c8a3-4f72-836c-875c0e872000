<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    
        <header>
        <div class="header-container">
            <div class="logo-area">
                <img src="images/logo.png" alt="شعار حزب بناء السودان" />
                <a href="#" class="logo-text">حزب بناء السودان</a>
            </div>

            <nav>
                <button class="mobile-menu-btn" onclick="toggleMobileMenu()" aria-label="فتح القائمة">
                    <i class="fas fa-bars"></i>
                </button>
                <ul class="nav-list" id="navList">
                    <li class="nav-item">
                        <a href="#" class="nav-link">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">عن الحزب</a>
                        <ul class="dropdown">
                            <li class="dropdown-item"><a href="#" class="dropdown-link">لمحة عن التأسيس</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">عابرون للأيدولوجيات</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">الدين والدولة</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">ملامح استراتيجية الحزب</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">الرؤية الوطنية</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">السياسة الداخلية لمعلومات الأعضاء</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">حلولنا من أجل السودان</a>
                        <div class="dropdown multi-column-dropdown">
                            <div class="dropdown-column">
                                <ul>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">العدالة للجميع</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">لقمة العيش والاقتصاد العادل</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">الصحة والعلاج</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">الزراعة المستدامة والأمن الغذائي</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">حماية البيئة</a></li>
                                </ul>
                            </div>
                            <div class="dropdown-column">
                                <ul>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">العمل والإنتاج</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">الكهرباء والوقود للتنمية</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">حماية الوطن</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">السودان بين الأمم</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">تراثنا والسياحة</a></li>
                                </ul>
                            </div>
                            <div class="dropdown-column">
                                <ul>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">التعليم وبناء المستقبل</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">الأمن والخدمات المدنية</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">البنية التحتية والنقل والاتصالات</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">الحكم المحلي</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">المؤسسات ومكافحة الفساد</a></li>
                                </ul>
                            </div>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">حكومة الظل</a>
                        <div class="dropdown multi-column-dropdown">
                            <div class="dropdown-column">
                                <ul>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة العدل</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة المالية والاقتصاد الوطني</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الصحة والرعاية الاجتماعية</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الخارجية</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الطاقة</a></li>
                                </ul>
                            </div>
                            <div class="dropdown-column">
                                <ul>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الداخلية</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الزراعة والري والثروة الحيوانية</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">البيئة والتنوع الحيوي</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">الثقافة والسياحة</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة البنية التحتية والنقل والطرق</a></li>
                                </ul>
                            </div>
                            <div class="dropdown-column">
                                <ul>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الدفاع</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الصناعة والتجارة</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة التعليم والبحث العلمي</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الإصلاح الإداري</a></li>
                                    <li class="dropdown-item"><a href="#" class="dropdown-link">وزارة الحكم المحلي</a></li>
                                </ul>
                            </div>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">الاتصال بنا</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">المزيد</a>
                        <ul class="dropdown">
                            <li class="dropdown-item"><a href="#" class="dropdown-link">الأكاديمية</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">الأخبار والتصريحات</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">صوت الشعب</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">الوظائف الشاغرة</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">بوابة الأعضاء</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">الفعاليات</a></li>
                            <li class="dropdown-item"><a href="#" class="dropdown-link">أرشيف السودان</a></li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <script>
    
        function toggleMobileMenu() {
            const navList = document.getElementById('navList');
            navList.classList.toggle('active');
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const navList = document.getElementById('navList');
            const mobileBtn = document.querySelector('.mobile-menu-btn');
            
            if (!navList.contains(event.target) && !mobileBtn.contains(event.target)) {
                navList.classList.remove('active');
            }
        });

        // Handle dropdown toggles on mobile
        document.querySelectorAll('.nav-item').forEach(item => {
            const link = item.querySelector('.nav-link');
            const dropdown = item.querySelector('.dropdown');
            
            if (dropdown) {
                link.addEventListener('click', (e) => {
                    if (window.innerWidth <= 768) {
                        e.preventDefault();
                        dropdown.classList.toggle('active');
                    }
                });
            }
        });

        // Close dropdowns when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768) {
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    if (!dropdown.contains(e.target) && !dropdown.previousElementSibling.contains(e.target)) {
                        dropdown.classList.remove('active');
                    }
                });
            }
        });
    
    </script>

        <!-- Code injected by live-server -->
	<script>
	// <![CDATA[  <-- For SVG support
	if ('WebSocket' in window) {
		(function () {
			function refreshCSS() {
				var sheets = [].slice.call(document.getElementsByTagName("link"));
				var head = document.getElementsByTagName("head")[0];
				for (var i = 0; i < sheets.length; ++i) {
					var elem = sheets[i];
					var parent = elem.parentElement || head;
					parent.removeChild(elem);
					var rel = elem.rel;
					if (elem.href && typeof rel != "string" || rel.length == 0 || rel.toLowerCase() == "stylesheet") {
						var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, '');
						elem.href = url + (url.indexOf('?') >= 0 ? '&' : '?') + '_cacheOverride=' + (new Date().valueOf());
					}
					parent.appendChild(elem);
				}
			}
			var protocol = window.location.protocol === 'http:' ? 'ws://' : 'wss://';
			var address = protocol + window.location.host + window.location.pathname + '/ws';
			var socket = new WebSocket(address);
			socket.onmessage = function (msg) {
				if (msg.data == 'reload') window.location.reload();
				else if (msg.data == 'refreshcss') refreshCSS();
			};
			if (sessionStorage && !sessionStorage.getItem('IsThisFirstTime_Log_From_LiveServer')) {
				console.log('Live reload enabled.');
				sessionStorage.setItem('IsThisFirstTime_Log_From_LiveServer', true);
			}
		})();
	}
	else {
		console.error('Upgrade your browser. This Browser is NOT supported WebSocket for Live-Reloading.');
	}
	// ]]
</script>
</body>
</html>